import React from "react";
import TextTypeFormEnginee from "./TextTypeFormEnginee";
import SingleSelectFormEnginee from "./SingleSelectFormEnginee";
import MultiSelectFormEnginee from "./MultiSelectFormEnginee";
import DatePickerFormEnginee from "./DatePickerFormEnginee";
import SelectOrTypeFormEnginee from "./SelectOrTypeFormEnginee";
import TextAreaFormEnginee from "./TextAreaFormEnginee";
import DateTimePickerFormEnginee from "./DateTimePickerFormEnginee";
import MultiDatePickerFormEnginee from "./MultiDatePickerFormEnginee";

const FormEnginee = ({
  form = [],
  formStateFunction = () => {},
  formState = {},
}) => {
  const updateFunction = (key, value, formDate) => {
    formStateFunction(key, value, formDate);
  };
  return (
    <div className=" w-full h-fit flex flex-col items-center gap-[1rem] pt-[1rem]">
      {form.map((current) => {
        return (
          <>
            {(current?.type === "text" ||
              current?.type === "email" ||
              current?.type === "number") && (
              <TextTypeFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "textarea" && (
              <TextAreaFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "singleselect" && (
              <SingleSelectFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "multipleselect" && (
              <MultiSelectFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "date" && (
              <DatePickerFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "multiDate" && (
              <MultiDatePickerFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "datetime" && (
              <DateTimePickerFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
            {current?.type === "selectortype" && (
              <SelectOrTypeFormEnginee
                state={formState[current?.key]}
                formData={current}
                updateFunction={updateFunction}
              />
            )}
          </>
        );
      })}
    </div>
  );
};

export default FormEnginee;

// DOCS

// Form Builder Obj
// Every obj represent one input field

// Evey obj has type which represents the type of input -> text, email, number, singleselect, multipleselect, selectortype , date

// type : "text" ( simple input field ) -> should have following fields
// key -> key to store the value ( BE will get the data in this key only )
// placeholder -> Text input place holder
// defaultValue -> if there is any defaultValue

// type : "email" ( simple input field ) -> should have following fields
// key -> key to store the value ( BE will get the data in this key only )
// placeholder -> Text input place holder
// defaultValue -> if there is any defaultValue

// type : "number" ( simple input field ) -> should have following fields
// key -> key to store the value ( BE will get the data in this key only )
// placeholder -> Text input place holder
// defaultValue -> if there is any defaultValue

// type : "textarea" ,
// key -> key to store the value ( BE will get the data in this key only )
// placeholder -> Text input place holder
// defaultValue -> If there is any defaultValue

// type : "singleselect" ( select input field -> take one input only ) -> should have following fields
// key -> key to store the value ( BE will get the data in this key only )
// options -> can be array of options or string "userList" and string "customerList" for listing users and customer as options
// defaultValue -> if there is any default value / placeholder values also
// placeholder -> this is validation purpose -> should allow the placeholder value

// type : "multipleselect" ( multipleselect input field -> take one or more inputs ) -> should have following fields
// key -> key to store the value ( BE will get the data in this key only )
// options -> can be array of options or string "userList" and string "customerList" for listing users and customer as options
// defaultValue -> if there are any default value / placeholder values also -> should be in array
// title -> text display when no input is selected -> place holder

// type : "selectortype" ( single select but can enter any value other than options ) - should have following fields
// key -> key to store the value ( BE will get the data in this key only )
// options -> can be array of options or string "userList" and string "customerList" for listing users and customer as options
// defaultValue -> if there is any default value / placeholder values also
// placeholder -> this is validation purpose -> should allow the placeholder value

// type : "date" ( date field )
// key -> key to store the value ( BE will get the data in this key only )
// title -> text to display in date picker
// defaultValue -> if there is any default value
// format : "date" or "format" -> "date" is for date obj / format is formatted string -> DD-MM-YYYY
