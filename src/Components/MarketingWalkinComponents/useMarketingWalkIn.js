
import { useDispatch, useSelector } from "react-redux";
import { parser_table_filter_data } from "../../UI_Enginee/TableHeaderEnginee/TableHeaderEnginee";
import { getStartAndEndDate } from "../../Config/DateFilterConfig";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../API/APIConfig";
import { PostAPI } from "../../API/PostAPI";
import { marketingWalkinSliceAction } from "../../Store/MarketingSlice/MarketingWalkinSlice";
import { GetAPI } from "../../API/GetAPI";
import { marketingWalkinFormConfig } from "../../Config/MarketingWalkinConfig";

export let dateObj = [
  {
    id: "Last year",
    content: "Last year",
  },
  {
    id: "Last Month",
    content: "Last Month",
  },
  {
    id: "Last Week",
    content: "Last Week",
  },
  {
    id: "Custom Date",
    content: "Custom Date",
  },
  {
    id: "Till now",
    content: "Till now",
  },
];

const useMarketingWalkIn = () => {
  const dispatch = useDispatch();
  const { filter, analytics, table } = useSelector((state) => {
    return state.marketingWalkin;
  });

  const master = useSelector((state) => {
    return state.master;
  });
  let auth = master?.auth;

  const fetch_list = (updated_filter_state = null, updated_date = null) => {
    console.log("=== Marketing Walkin fetch_list called ===");
    console.log("updated_filter_state:", updated_filter_state);
    console.log("updated_date:", updated_date);

    if (!auth?.id) {
      console.log("No auth ID, returning early");
      return;
    }

    let filterObj = parser_table_filter_data(table.header, filter.state);
    let filterState = filterObj?.state;
    let isFilter = filterObj?.isFilter;

    if (updated_filter_state) {
      let filterState_parser = parser_table_filter_data(
        table.header,
        updated_filter_state
      );
      filterState = filterState_parser?.state;
      isFilter = filterState_parser?.isFilter;
    }

    let customDate = "Custom Date";
    let startDate = filter?.date?.startDate;
    let endDate = filter?.date?.endDate;
    let filter_options = filter?.date?.filter?.content;

    if (updated_date) {
      startDate = updated_date?.startDate;
      endDate = updated_date?.endDate;
      filter_options = updated_date?.filter?.content;
    }

    if (filter_options === "Last year") {
      let [startD, endD] = getStartAndEndDate(365);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Last Month") {
      let [startD, endD] = getStartAndEndDate(30);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Last Week") {
      let [startD, endD] = getStartAndEndDate(7);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Till now") {
      let [startD, endD] = getStartAndEndDate(3650 + 3650);
      startDate = new Date(startD);
      endDate = new Date(endD);
    } else if (filter_options === "Custom Date") {
    }

    console.log("Final date parameters:");
    console.log("startDate:", startDate);
    console.log("endDate:", endDate);
    console.log("filter_options:", filter_options);
    console.log("isFilter:", isFilter);
    console.log("filterState:", filterState);

    if (isFilter) {
      dispatch(
        marketing_walkin_filter_list(filterState, [startDate, endDate], auth)
      );
    } else {
      dispatch(marketing_walkin_filter_list({}, [startDate, endDate], auth));
    }

    // Pass date parameters to header data thunk to make it responsive to date filters
    dispatch(marketing_walkin_headerdata_thunk(auth, startDate, endDate));
  };

  // table header data load
  const loadTableHeaderData = () => {
    // dispatch(marketing_loadTableHeaderData_thunk(table?.header));
  };

  return {
    loadTableHeaderData,
    fetch_list,
  };
};

export default useMarketingWalkIn;

function formatDateToYYYYMMDD(dateObj) {
  if (!(dateObj instanceof Date)) {
    // throw new Error("Invalid input: Expected a Date object");
    return;
  }
  dateObj = new Date(dateObj);
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(dateObj.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

export const marketing_walkin_filter_list = (
  filterObj,
  [startDate, endDate],
  auth
) => {
  return async (dispatch) => {
    try {
      let url = new URL(
        BaseURL?.mainURl + API_ENDPOINTS?.marketing_walkin_filter_list
      );

      url =
        url.href +
        "?startDate=" +
        formatDateToYYYYMMDD(new Date(startDate)) +
        "&tillDate=" +
        formatDateToYYYYMMDD(new Date(endDate)) +
        "&userId=" +
        auth?.id;

      let headers = { ...api_headers };

      let body = filterObj;

      console.log("Marketing Walkin Filter API Call:", url.toString());
      console.log("Filter Body:", body);

      let response = await PostAPI(url, body, headers);

      if (response?.ok) {
        let data = await response?.json();
        console.log("Marketing Walkin API Response:", data);

        // Handle different possible response structures
        let array = [];

        if (data?.data?.assignedBy && data?.data?.assignedTo) {
          // If the API returns assignedBy/assignedTo structure (like marketing leads)
          array = [...data?.data?.assignedBy, ...data?.data?.assignedTo];
        } else if (data?.data && Array.isArray(data?.data)) {
          // If the API returns data as a direct array
          array = data?.data;
        } else if (data?.data?.walkins && Array.isArray(data?.data?.walkins)) {
          // If the API returns walkins array
          array = data?.data?.walkins;
        } else if (data?.data?.list && Array.isArray(data?.data?.list)) {
          // If the API returns list array
          array = data?.data?.list;
        } else {
          console.warn("Unexpected API response structure:", data);
          array = [];
        }

        console.log("Processed walkin array:", array);
        dispatch(marketingWalkinSliceAction.updateList(array?.reverse() || []));
      } else {
        console.error("API request failed:", response.status, response.statusText);
        dispatch(marketingWalkinSliceAction.updateList([]));
      }
    } catch (error) {
      console.error("Error in marketing_walkin_filter_list:", error);
      dispatch(marketingWalkinSliceAction.updateList([]));
    }
  };
};

export const marketing_walkin_headerdata_thunk = (auth, startDate = null, endDate = null) => {
  return async (dispatch) => {
    try {
      let url = new URL(
        BaseURL?.mainURl + API_ENDPOINTS?.marketing_walkin_filter_list
      );

      // Add date parameters to make header data responsive to date filters
      if (startDate && endDate) {
        url = url.href +
          "?startDate=" + formatDateToYYYYMMDD(new Date(startDate)) +
          "&tillDate=" + formatDateToYYYYMMDD(new Date(endDate)) +
          "&userId=" + auth?.id;
      } else {
        // Default to "Till now" if no dates provided
        let [defaultStartDate, defaultEndDate] = getStartAndEndDate(3650 + 3650);
        url = url.href +
          "?startDate=" + formatDateToYYYYMMDD(new Date(defaultStartDate)) +
          "&tillDate=" + formatDateToYYYYMMDD(new Date(defaultEndDate)) +
          "&userId=" + auth?.id;
      }

      console.log("Marketing Walkin Header API Call:", url.toString());

      let headers = { ...api_headers };

      let response = await PostAPI(url, {}, headers);

      if (response?.ok) {
        let responsedata = await response?.json();
        console.log("Marketing Walkin Header API Response:", responsedata);

        let leadHeader = responsedata?.data?.header;

        let array = [
          { title: "TOTAL", count: leadHeader["TOTAL"] || 0 },
          { title: "NEW LEAD", count: leadHeader["NEW LEAD"] || 0 },
          { title: "QUOTE SENT", count: leadHeader["QUOTE SENT"] || 0 },
          { title: "IN FOLLOW UP", count: leadHeader["IN FOLLOW UP"] || 0 },
          { title: "DROPPED", count: leadHeader["DROPPED"] || 0 },
          { title: "ORDER CLOSED", count: leadHeader["ORDER CLOSED"] || 0 },
        ];

        console.log("Processed header array:", array);
        dispatch(marketingWalkinSliceAction.updateHeaderList(array));
      } else {
        console.error("Header API request failed:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error in marketing_walkin_headerdata_thunk:", error);
    }
  };
};

// add form Builder
export const fetch_marketing_walkin_add_form_builder_function = async () => {
  return marketingWalkinFormConfig;
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.marketing_walkin_AddFormBuilder
  );

  let headers = { ...api_headers };

  let response = await GetAPI(url, headers);

  if (response?.ok) {
    let responseData = await response?.json();
    return responseData?.structure;
  }
  // return salesandmarketing_addForm;

  return [];
};

// Edit

const lead_edit_form = (editObj) => {
  let array = [];
  for (let item of marketingWalkinFormConfig) {
    let obj = { ...item };

    obj.defaultValue = editObj[obj?.key];

    array.push(obj);
  }
  return array;
};
export const fetch_marketing_walkin_edit_form_enginee = async (id) => {
  //   return marketingWalkinFormConfig;
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.marketing_walkin_EditFormBuilder
  );

  let headers = { ...api_headers };

  let body = {
    id: id,
  };

  let response = await PostAPI(url, body, headers);

  if (response?.ok) {
    let responseData = await response?.json();

    let editform = lead_edit_form(responseData?.data);

    return editform;
  }

  return marketingWalkinFormConfig;
  return [];
};
