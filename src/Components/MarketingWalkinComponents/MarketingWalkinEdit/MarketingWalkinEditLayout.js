import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import useMarketingWalkIn, {
  fetch_marketing_walkin_edit_form_enginee,
  fetch_sales_edit_form_enginee,
} from "../useMarketingWalkIn";
import UseRouterAuthentication from "../../../Hooks/UseRouterAuthentication";
import {
  formData_parser,
  formEnginee_edit_Formatee_Structure_and_State,
} from "../../../Dashboard_API_calling_and_formatting/Form_Formate_and_API";
import { marketingWalkinSliceAction } from "../../../Store/MarketingSlice/MarketingWalkinSlice";
import { routes } from "../../../Config/routesConfig";
import toast from "react-hot-toast";
import ButtonComponent from "../../../BasicUIElements/ButtonComponent";
import FormEnginee from "../../../UI_Enginee/Form/FormEnginee";
import ModelHeaderAndRoute from "../../Utils/UtilsComponents/ModelHeaderAndRoute";
import Spinner from "../../../BasicUIElements/Spinner";
import { API_ENDPOINTS, api_headers, BaseURL } from "../../../API/APIConfig";
import { PostAPI } from "../../../API/PostAPI";

const MarketingWalkinEditLayout = () => {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { fetch_list } = useMarketingWalkIn();

  const { edit } = useSelector((state) => {
    return state.marketingWalkin;
  });

  const { routeAuthentication } = UseRouterAuthentication();
  const { auth, access } = useSelector((state) => {
    return state.master;
  });

  const [_id, set_Id] = useState("");
  const [state, setState] = useState({});
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let id = searchParams?.get("id");

    let fetch = async () => {
      setIsContentLoading(true);

      let result = await fetch_marketing_walkin_edit_form_enginee(id);
      let ss = formEnginee_edit_Formatee_Structure_and_State(result);
      setState(ss.state);
      dispatch(
        marketingWalkinSliceAction.updateEditStructureAndState({
          ...ss,
          id: id,
        })
      );

      setIsContentLoading(false);

      if (ss.structure?.length === 0) {
        return false;
      } else {
        return true;
      }
    };

    if (searchParams?.get("id") !== _id) {
      fetch();
      set_Id(searchParams?.get("id"));
    } else {
      setState(edit.state);
    }
  }, [searchParams?.get("id")]);

  const updateFunctionOfForm = (key, value) => {
    setState((state) => {
      return { ...state, [key]: { ...state[key], value: value } };
    });
  };

  const closeModal = () => {
    navigate(routes?.marketingWalkin?.directLink);
  };

  const clickHandler = async () => {
    let parser_obj = formData_parser(edit.structure, state);

    setIsLoading(true);

    let toast_id = toast.loading("Updating", { position: "top-right" });

    let response = await marketing_walkin_edit(
      parser_obj?.parser_obj,
      searchParams?.get("id")
    );

    if (response?.ok) {
      fetch_list();
      closeModal();
      toast.success("Updated", { id: toast_id });
    } else {
      let responsedata = await response.json();
      toast.error(responsedata?.error, { id: toast_id });
    }
    setIsLoading(false);
  };

  return (
    <>
      {isContentLoading ? (
        <Spinner />
      ) : (
        <div className=" md:w-[424px] w-[340px] max-h-[95vh] h-fit overflow-y-auto bg-[#fff] rounded-[6px] p-4 pb-[4rem] ">
          <ModelHeaderAndRoute
            className="w-full h-fit flex flex-row justify-between ps-4 pe-4 "
            title="Edit The Lead"
            onClick={() => {
              closeModal();
            }}
          />

          <FormEnginee
            form={edit?.structure}
            formStateFunction={updateFunctionOfForm}
            formState={state}
          />

          <div className=" w-full h-fit mt-[1rem]">
            <div className=" w-full h-fit flex flex-row justify-center gap-[1rem]">
              <ButtonComponent
                onClick={() => {
                  closeModal();
                }}
                className=" w-[45%] h-[58px] rounded-[6px] border-[#00BD94] border-2 bg-clip-text text-transparent bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] flex flex-row justify-center items-center"
              >
                Cancel
              </ButtonComponent>
              <ButtonComponent
                onClick={clickHandler}
                isLoading={isLoading}
                className=" w-[45%] h-[58px] rounded-[6px] bg-mainLinearGradient md:text-[20px] text-[16px] font-inter font-[500] text-[#fff] flex flex-row justify-center items-center"
              >
                Edit The Lead
              </ButtonComponent>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MarketingWalkinEditLayout;

const marketing_walkin_edit = async (content, id) => {
  let url = new URL(
    BaseURL?.mainURl + API_ENDPOINTS?.marketing_walkin_Edit + "/" + id
  );

  let body = content;

  let headers = { ...api_headers };

  let response = await PostAPI(url, body, headers, "PUT");

  return response;
};
